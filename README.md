# MyCopilot - AI Assistant 🚀

An AI-powered assistant that provides real-time transcription and intelligent responses during conversations and meetings, featuring support for the latest AI models including **GPT-5**, **Gemini 2.5**, and advanced reasoning models.

[![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://opensource.org/licenses/MIT)

## ✨ Features

- 🎙️ **Real-time Transcription**: High-accuracy voice-to-text for all participants using advanced speech recognition services
- 🤖 **Latest AI Models**: Support for cutting-edge models including:
  - **GPT-5 Family** (GPT-5, GPT-5 Mini, GPT-5 Nano) - *Just Released Aug 2025*
  - **OpenAI o1 Reasoning Models** (o1, o1-mini)
  - **Gemini 2.5 Family** (Pro, Flash, Flash Lite) with thinking capabilities
  - **GPT-4o Family** and legacy models
- 🧠 **Advanced AI Parameters**:
  - Reasoning effort control for GPT-5 models
  - Response verbosity settings
  - Thinking budget for Gemini 2.5 models
- 🖼️ **Picture-in-Picture Mode**: Keep AI responses visible in a floating window
- 💻 **Code Formatting**: Syntax highlighting for technical content
- ✨ **Modern UI**: Clean, professional interface with indigo color scheme
- 📚 **Question History**: Combine multiple questions for comprehensive AI analysis
- ⏱️ **Smart Silence Detection**: Automatic submission after configurable silence periods
- ⚙️ **Highly Configurable**: Customize everything from models to prompts to UI behavior

## 🛠️ Technologies Used

- **Frontend**: React 19, Redux Toolkit, Material-UI 6.x
- **AI Services**:
  - OpenAI API (GPT-5, GPT-4o, o1 models)
  - Google Generative AI (Gemini 2.5, 2.0, 1.5)
  - Azure Cognitive Services (Speech-to-Text)
- **Build Tools**: Next.js 15, npm
- **Styling**: Material-UI with custom theming, Emotion
- **Other Libraries**: React Markdown, Highlight.js, Microsoft Speech SDK

## 🤖 Supported AI Models

### OpenAI Models
- **GPT-5 Family** *(Latest - Aug 2025)*
  - `gpt-5` - Latest & Best for Coding/Agentic tasks
  - `gpt-5-mini` - Fast & Efficient (Recommended default)
  - `gpt-5-nano` - Fastest & Cheapest
  - `gpt-5-chat-latest` - Non-reasoning version
- **Reasoning Models**
  - `o1` - Advanced reasoning capabilities
  - `o1-mini` - Fast reasoning
- **GPT-4 Family**
  - `gpt-4o` - Multimodal capabilities
  - `gpt-4o-mini` - Fast and efficient
  - `gpt-4-turbo`, `gpt-4` - Still supported
- **Legacy**: `gpt-3.5-turbo`

### Google Gemini Models
- **Gemini 2.5 Family** *(Latest & Best)*
  - `gemini-2.5-pro` - Highest quality
  - `gemini-2.5-flash` - Recommended balance
  - `gemini-2.5-flash-lite` - Fast processing
- **Gemini 2.0 Family**
  - `gemini-2.0-flash`, `gemini-2.0-flash-lite`
- **Gemini 1.5 Family** *(Deprecated but supported)*
  - `gemini-1.5-flash`, `gemini-1.5-flash-8b`, `gemini-1.5-pro`

## 🚀 Getting Started

### Prerequisites

- Node.js (v18+)
- npm (v9+)
- **OpenAI API key**: Get your key from [OpenAI Platform](https://platform.openai.com/docs/overview)
- **Gemini API key**: Get your key from [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Azure Speech Service key**: Get a free trial key from [Microsoft Azure](https://azure.microsoft.com/en-us/pricing/purchase-options/azure-account)

### Installation

#### Option 1: Docker (Production) 🐳

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/mycopilot.git
   cd mycopilot
   ```

2. **Configure API keys**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

3. **Run with Docker**
   ```bash
   ./docker-setup.sh start
   ```

4. **Access the application**
   - Open: `http://localhost:3939`
   - *Note: Uses port 3939 to avoid conflicts with other containers*

#### Option 2: Local Development (Recommended for Development)

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/mycopilot.git
   cd mycopilot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Access the application**
   Open your browser to `http://localhost:3001` (or the port shown in terminal)

## ⚙️ Configuration

### Basic Setup
1. Open the **Settings** dialog (⚙️ icon in the header)
2. Enter your API credentials:
   - **OpenAI API Key** (for GPT-5, GPT-4o, o1 models)
   - **Gemini API Key** (for Gemini 2.5, 2.0, 1.5 models)
   - **Azure Speech Service Key** (for transcription)
   - **Azure Region** (e.g., eastus)

### Model Configuration
3. **Choose your AI model** from the dropdown:
   - **Recommended**: `gpt-5-mini` (default) - Best balance of performance and cost
   - **Best Quality**: `gpt-5` or `gemini-2.5-pro`
   - **Fastest**: `gpt-5-nano` or `gemini-2.5-flash-lite`
   - **Reasoning**: `o1` or `o1-mini` for complex analysis

### Advanced Settings
4. **Configure advanced parameters** (model-specific):
   - **Reasoning Effort** (GPT-5 models): `minimal`, `low`, `medium`, `high`
   - **Response Verbosity** (GPT-5 models): `low`, `medium`, `high`
   - **Thinking Budget** (Gemini 2.5 models): Default, Disabled, or Custom budget

5. **General preferences**:
   - AI System Prompt customization
   - Response Length (concise, medium, lengthy)
   - Silence Timer Duration
   - Auto-Submit vs Manual modes

## 🐳 Docker Commands

### Quick Commands
```bash
# Start the application
./docker-setup.sh start

# View logs
./docker-setup.sh logs

# Stop container
./docker-setup.sh stop

# Clean up everything
./docker-setup.sh cleanup
```

### Manual Docker Commands
```bash
# Build and run
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop container
docker-compose down
```

## 🖥️ Usage

### Main Interface Components

1. **System Audio Panel (Left)**
   - Start/Stop system audio capture for the interviewer
   - View and edit transcribed questions
   - Manage and combine questions from history
   - Real-time transcription display

2. **AI Assistant Log (Center)**
   - View real-time AI responses with streaming
   - Code formatting and syntax highlighting
   - Complete response history
   - Auto-scroll toggle and Picture-in-Picture mode
   - Copy responses and export functionality

3. **Your Mic Panel (Right)**
   - Start/Stop your microphone for candidate audio
   - Toggle between auto-submit and manual input modes
   - Manual submission of responses to AI
   - Voice activity indicators

### Advanced Features

- **Question History**: Combine multiple questions for comprehensive AI analysis
- **Picture-in-Picture**: Keep AI responses visible while using other applications
- **Smart Transcription**: Automatic silence detection and submission
- **Model Switching**: Change AI models on-the-fly without losing context
- **Export Options**: Save conversations and responses for later review

## 🛠️ Troubleshooting

### Common Issues

1. **Audio Permissions**
   - Ensure your browser has microphone access
   - If permissions were denied, refresh the page and allow access when prompted
   - Check browser settings for microphone permissions

2. **API Errors**
   - Double-check that your API keys in settings are correct
   - Verify your internet connection
   - Ensure the correct API key is provided for the selected AI model:
     - OpenAI key for GPT-5, GPT-4o, o1 models
     - Gemini key for Gemini 2.5, 2.0, 1.5 models
   - Check API quotas and billing status

3. **Model-Specific Issues**
   - **GPT-5 models**: Ensure you have access to the latest models (released Aug 2025)
   - **o1 models**: These use non-streaming responses, expect different behavior
   - **Gemini 2.5**: Thinking budget settings may affect response time

4. **Transcription Issues**
   - Speak clearly with minimal background noise
   - Verify Azure Speech Service subscription is active
   - Check microphone quality and positioning
   - Ensure proper Azure region configuration

5. **Performance Issues**
   - Try switching to faster models (e.g., `gpt-5-nano`, `gemini-2.5-flash-lite`)
   - Adjust reasoning effort to `minimal` or `low` for faster responses
   - Clear browser cache if experiencing UI issues

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **OpenAI** for their cutting-edge GPT-5, GPT-4o, and o1 models
- **Google** for the advanced Gemini 2.5 models with thinking capabilities
- **Microsoft Azure** for reliable Cognitive Services
- **Material-UI team** and the React community for excellent development tools

## � Recent Updates

### August 2025
- ✅ **GPT-5 Support**: Added support for all GPT-5 models (just released)
- ✅ **Gemini 2.5**: Full integration with thinking capabilities
- ✅ **Advanced Parameters**: Reasoning effort, verbosity, thinking budget controls
- ✅ **Modern UI**: Updated with professional indigo color scheme
- ✅ **Enhanced Security**: Updated all dependencies and fixed vulnerabilities
- ✅ **Better Performance**: Optimized for latest model capabilities

---

**Built with ❤️ for better technical interviews**
