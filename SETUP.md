# MyCopilot Setup Guide

## 🔐 API Keys Configuration

For security, API keys are now configured via environment variables instead of being stored in the browser's localStorage.

### 1. Create Environment File

Copy the example environment file:
```bash
cp .env.example .env.local
```

### 2. Add Your API Keys

Edit `.env.local` and add your API keys:

```bash
# Speech Services API Keys
NEXT_PUBLIC_DEEPGRAM_API_KEY=your_deepgram_api_key_here
NEXT_PUBLIC_AZURE_SPEECH_KEY=your_azure_speech_key_here
NEXT_PUBLIC_AZURE_SPEECH_REGION=your_azure_region_here

# AI Model API Keys
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here
```

### Why NEXT_PUBLIC_ prefix?

The `NEXT_PUBLIC_` prefix is **required by Next.js** for environment variables that need to be accessible in the browser. Since our speech recognition and AI features run client-side, these API keys need to be available to the browser JavaScript.

**Security Note**: In production, consider moving API calls to server-side API routes for better security.

### 3. Get Your API Keys

#### Deepgram (Recommended for Speech Recognition)
1. Go to [Deepgram Console](https://console.deepgram.com/)
2. Sign up for a free account
3. Create a new API key
4. Copy the key to `NEXT_PUBLIC_DEEPGRAM_API_KEY`

#### Azure Speech Services (Alternative)
1. Go to [Azure Portal](https://portal.azure.com/)
2. Create a Speech Services resource
3. Get your subscription key and region
4. Add them to `NEXT_PUBLIC_AZURE_SPEECH_KEY` and `NEXT_PUBLIC_AZURE_SPEECH_REGION`

#### OpenAI (For AI Responses)
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an API key
3. Add it to `NEXT_PUBLIC_OPENAI_API_KEY`

#### Google Gemini (Alternative AI)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create an API key
3. Add it to `NEXT_PUBLIC_GEMINI_API_KEY`

### 4. Restart the Development Server

After adding your API keys, restart the development server:
```bash
npm run dev
```

## 🚀 Running the Application

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure API keys** (see above)

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Open in browser:**
   ```
   http://localhost:3001/interview
   ```

## ✅ Verify Configuration

1. Open the application
2. Click the Settings icon (⚙️)
3. Check the "API Keys Configuration" section
4. You should see "Active" status for configured services

## 🔒 Security Benefits

- **No API keys in localStorage**: Keys are never stored in the browser
- **Environment variables**: Keys are kept secure on the server side
- **No accidental commits**: `.env.local` is in `.gitignore`
- **Easy deployment**: Environment variables work seamlessly in production

## 🛠️ Troubleshooting

### API Key Status Shows "Not Configured"
- Check that your `.env.local` file exists
- Verify the environment variable names are correct
- Restart the development server after making changes

### Speech Recognition Not Working
- Ensure microphone permissions are granted
- Check that the appropriate speech service API key is configured
- Try switching between Deepgram and Azure in Settings

### AI Responses Not Working
- Verify OpenAI or Gemini API key is configured
- Check that you have sufficient API credits
- Try switching between different AI models in Settings

## 📁 File Structure

```
mycopilot/
├── .env.local          # Your API keys (create this)
├── .env.example        # Template for API keys
├── pages/
│   └── interview.js    # Main application page
├── components/
│   └── SettingsDialog.js  # Settings with API key status
└── utils/
    └── config.js       # Configuration management
```

## 🔄 Migration from localStorage

If you previously had API keys stored in localStorage, they will be automatically removed for security when you first open the Settings dialog after this update.

## 🚀 Production Deployment

For production deployment, set the environment variables in your hosting platform:
- Vercel: Add environment variables in the Vercel dashboard
- Netlify: Add environment variables in site settings
- Docker: Use environment variables in your container configuration

The application will automatically use these environment variables in production.
