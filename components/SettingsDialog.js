import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import {
    Box,
    Button,
    Checkbox,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControl,
    FormControlLabel,
    FormHelperText,
    Grid,
    IconButton,
    InputLabel,
    ListSubheader,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { builtInModelGroups, getConfig, setConfig, speechServices } from '../utils/config';

export default function SettingsDialog({ open, onClose, onSave }) {
  const [settings, setSettings] = useState(getConfig());
  const [newModelName, setNewModelName] = useState('');
  const [newModelId, setNewModelId] = useState('');
  const [newModelType, setNewModelType] = useState('openai'); // 'openai' or 'gemini'
  const [apiKeyStatus, setApiKeyStatus] = useState(null);

  useEffect(() => {
    if (open) {
      setSettings(getConfig());
      // Load API key status from environment variables
      const { getApiKeyStatus } = require('../utils/config');
      setApiKeyStatus(getApiKeyStatus());
      // Reset new model fields when dialog opens
      setNewModelName('');
      setNewModelId('');
      setNewModelType('openai');
    }
  }, [open]);

  const handleChange = (e) => {
    setSettings({ ...settings, [e.target.name]: e.target.value });
  };

  const handleAddNewModel = () => {
    if (!newModelName.trim() || !newModelId.trim()) {
      alert('Please provide both a display name and an ID for the new model.');
      return;
    }
    const newModel = { label: newModelName.trim(), value: newModelId.trim(), type: newModelType };
    const updatedCustomModels = [...(settings.customModels || []), newModel];
    setSettings({ ...settings, customModels: updatedCustomModels });
    setNewModelName('');
    setNewModelId('');
    // Keep newModelType for potentially adding another of the same type
  };

  const handleRemoveCustomModel = (indexToRemove) => {
    const updatedCustomModels = (settings.customModels || []).filter((_, index) => index !== indexToRemove);
    // If the currently selected model was the one removed, reset to a default
    let currentAiModel = settings.aiModel;
    if (settings.customModels[indexToRemove]?.value === currentAiModel) {
        currentAiModel = builtInModelGroups[0]?.models[0]?.value || 'gpt-3.5-turbo'; // Fallback
    }
    setSettings({ ...settings, customModels: updatedCustomModels, aiModel: currentAiModel });
  };


  const handleSave = () => {
    // Validate model-key pairing
    const selectedModelValue = settings.aiModel;
    let selectedModelIsGemini = selectedModelValue.startsWith('gemini');

    // Check if the selected model is a custom Gemini model
    const customGeminiModel = (settings.customModels || []).find(m => m.value === selectedModelValue && m.type === 'gemini');
    if (customGeminiModel) {
        selectedModelIsGemini = true;
    }
     // Check if the selected model is a custom OpenAI model
    const customOpenAIModel = (settings.customModels || []).find(m => m.value === selectedModelValue && m.type === 'openai');
     if (customOpenAIModel && !selectedModelIsGemini) { // if it's custom and not already flagged as gemini
        // it's an OpenAI type model
     }


    // API key validation is now handled by environment variables
    // The application will show appropriate error messages if keys are missing

    setConfig(settings); // Uses the setConfig from utils/config.js
    if (onSave) onSave();
    onClose();
  };


  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth PaperProps={{ sx: { borderRadius: 3 } }}>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid', borderColor: 'divider', pb: 1.5 }}>
        Application Settings
        <IconButton aria-label="close" onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 2.5 }}>
        <Typography variant="h6" gutterBottom>API Keys Configuration</Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          API keys are now configured via environment variables for security.
          Add your keys to the .env.local file and restart the server.
        </Typography>

        {apiKeyStatus && (
          <Box sx={{ mb: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="subtitle2">OpenAI API</Typography>
                  <Typography
                    variant="body2"
                    color={apiKeyStatus.openai.configured ? 'success.main' : 'error.main'}
                  >
                    {apiKeyStatus.openai.status}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="subtitle2">Gemini API</Typography>
                  <Typography
                    variant="body2"
                    color={apiKeyStatus.gemini.configured ? 'success.main' : 'error.main'}
                  >
                    {apiKeyStatus.gemini.status}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />
        <Typography variant="h6" gutterBottom>AI Configuration</Typography>
        <FormControl fullWidth margin="dense">
          <InputLabel id="ai-model-select-label">AI Model</InputLabel>
          <Select
            labelId="ai-model-select-label" name="aiModel" value={settings.aiModel}
            onChange={handleChange} label="AI Model"
          >
            {builtInModelGroups.map(group => ([
              <ListSubheader key={group.name} sx={{ fontWeight: 'bold', color: 'text.primary', bgcolor: 'transparent', lineHeight: '2.5em' }}>
                {group.name}
              </ListSubheader>,
              ...group.models.map(model => (
                <MenuItem key={model.value} value={model.value}>{model.label}</MenuItem>
              ))
            ]))}
            {(settings.customModels && settings.customModels.length > 0) && (
              <ListSubheader sx={{ fontWeight: 'bold', color: 'text.primary', bgcolor: 'transparent', lineHeight: '2.5em', mt: 1 }}>
                Custom Models
              </ListSubheader>
            )}
            {(settings.customModels || []).map((model, index) => (
              <MenuItem key={`custom-${model.value}-${index}`} value={model.value}>
                {model.label} ({model.type === 'gemini' ? 'Gemini' : 'OpenAI'})
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          fullWidth margin="dense" name="gptSystemPrompt" label="AI System Prompt" multiline rows={3}
          value={settings.gptSystemPrompt} onChange={handleChange} helperText="Instructions for the AI assistant." sx={{mt:2}}
        />
        <FormControl fullWidth margin="dense" sx={{mt:2}}>
            <InputLabel id="response-length-label">AI Response Length</InputLabel>
            <Select
                labelId="response-length-label" name="responseLength" value={settings.responseLength}
                onChange={handleChange} label="AI Response Length"
            >
                <MenuItem value="concise">Concise (1-2 sentences, essential info only)</MenuItem>
                <MenuItem value="medium">Medium (2-3 sentences, key details)</MenuItem>
                <MenuItem value="lengthy">Lengthy (More comprehensive, but still focused)</MenuItem>
            </Select>
        </FormControl>

        <Divider sx={{ my: 3 }} />
        <Typography variant="h6" gutterBottom>Manage Custom AI Models</Typography>
        <Box sx={{p:2, border: '1px dashed', borderColor: 'divider', borderRadius: 1, mb:2}}>
            <Typography variant="subtitle1" gutterBottom>Add New Model</Typography>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={4}>
                    <TextField fullWidth margin="dense" label="Model Display Name" value={newModelName} onChange={(e) => setNewModelName(e.target.value)} />
                </Grid>
                <Grid item xs={12} sm={4}>
                    <TextField fullWidth margin="dense" label="Model ID / Path" value={newModelId} onChange={(e) => setNewModelId(e.target.value)} />
                </Grid>
                <Grid item xs={12} sm={3}>
                     <FormControl component="fieldset" margin="dense">
                        <RadioGroup row name="newModelType" value={newModelType} onChange={(e) => setNewModelType(e.target.value)}>
                            <FormControlLabel value="openai" control={<Radio size="small"/>} label="OpenAI" />
                            <FormControlLabel value="gemini" control={<Radio size="small"/>} label="Gemini" />
                        </RadioGroup>
                    </FormControl>
                </Grid>
                <Grid item xs={12} sm={1}>
                    <Tooltip title="Add Model">
                        <IconButton color="primary" onClick={handleAddNewModel} disabled={!newModelName.trim() || !newModelId.trim()}>
                            <AddCircleOutlineIcon />
                        </IconButton>
                    </Tooltip>
                </Grid>
            </Grid>
        </Box>
         {(settings.customModels && settings.customModels.length > 0) && (
            <Box>
                <Typography variant="subtitle1" gutterBottom>Your Custom Models:</Typography>
                <List dense>
                {(settings.customModels || []).map((model, index) => (
                    <ListItem
                        key={index}
                        secondaryAction={
                            <Tooltip title="Remove Model">
                            <IconButton edge="end" aria-label="delete" onClick={() => handleRemoveCustomModel(index)} size="small">
                                <DeleteIcon fontSize="small"/>
                            </IconButton>
                            </Tooltip>
                        }
                        sx={{mb:0.5, bgcolor: 'action.hover', borderRadius: 1, p:1}}
                    >
                        <ListItemText primary={model.label} secondary={`${model.value} (${model.type === 'gemini' ? 'Gemini' : 'OpenAI'})`} />
                    </ListItem>
                ))}
                </List>
            </Box>
        )}


        <Divider sx={{ my: 3 }} />
        <Typography variant="h6" gutterBottom>Advanced Model Settings</Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          These settings are for future advanced models and may not affect current models.
        </Typography>

        <FormControl fullWidth margin="dense">
          <InputLabel>Reasoning Effort (Future Models)</InputLabel>
          <Select
            value={settings.reasoningEffort || 'medium'}
            onChange={(e) => setSettings({...settings, reasoningEffort: e.target.value})}
            label="Reasoning Effort (Future Models)"
          >
            <MenuItem value="low">Low (Fast)</MenuItem>
            <MenuItem value="medium">Medium (Balanced)</MenuItem>
            <MenuItem value="high">High (Thorough)</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
          <InputLabel>Response Verbosity (Future Models)</InputLabel>
          <Select
            value={settings.verbosity || 1}
            onChange={(e) => setSettings({...settings, verbosity: parseInt(e.target.value)})}
            label="Response Verbosity (Future Models)"
          >
            <MenuItem value={0}>Concise</MenuItem>
            <MenuItem value={1}>Balanced</MenuItem>
            <MenuItem value={2}>Detailed</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
          <InputLabel>Thinking Budget (Gemini 2.5 Models)</InputLabel>
          <Select
            value={settings.thinkingBudget === null ? 'default' : settings.thinkingBudget}
            onChange={(e) => {
              const value = e.target.value;
              setSettings({
                ...settings,
                thinkingBudget: value === 'default' ? null : parseInt(value)
              });
            }}
            label="Thinking Budget (Gemini 2.5 Models)"
          >
            <MenuItem value="default">Default (Thinking Enabled)</MenuItem>
            <MenuItem value={0}>Disabled (Faster, Less Thoughtful)</MenuItem>
            <MenuItem value={1000}>Low Budget</MenuItem>
            <MenuItem value={5000}>Medium Budget</MenuItem>
            <MenuItem value={10000}>High Budget</MenuItem>
          </Select>
        </FormControl>

        <Divider sx={{ my: 3 }} />
        <Typography variant="h6" gutterBottom>Speech Configuration</Typography>

        {/* Speech Service Selection */}
        <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
          <InputLabel>Speech Service</InputLabel>
          <Select
            name="speechService"
            value={settings.speechService || 'deepgram'}
            onChange={handleChange}
            label="Speech Service"
          >
            {speechServices.map((service) => (
              <MenuItem key={service.value} value={service.value}>
                {service.label}
              </MenuItem>
            ))}
          </Select>
          <FormHelperText>
            Deepgram is recommended for better accuracy and speed
          </FormHelperText>
        </FormControl>

        <TextField
          fullWidth margin="dense" name="silenceTimerDuration" label="Silence Detection (seconds)"
          type="number" inputProps={{ step: 0.1, min: 0.5, max: 5 }} value={settings.silenceTimerDuration}
          onChange={handleChange} helperText="Auto-submit after this duration of silence (e.g., 1.2)."
          sx={{ mt: 2 }}
        />

        <FormControl component="fieldset" sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Audio Processing Mode</Typography>
          <RadioGroup
            name="completeListeningMode"
            value={settings.completeListeningMode ? 'complete' : 'auto'}
            onChange={(e) => setSettings({...settings, completeListeningMode: e.target.value === 'complete'})}
          >
            <FormControlLabel
              value="auto"
              control={<Radio />}
              label="Auto-submit on silence (Default)"
            />
            <FormControlLabel
              value="complete"
              control={<Radio />}
              label="Complete listening mode (Manual submission only)"
            />
          </RadioGroup>
          <FormHelperText>
            Complete listening mode waits for you to manually submit instead of auto-submitting on silence.
          </FormHelperText>
        </FormControl>

        <FormControl component="fieldset" sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Text Processing</Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={settings.fillerSoundFiltering}
                onChange={(e) => setSettings({...settings, fillerSoundFiltering: e.target.checked})}
              />
            }
            label="Filter filler sounds (um, uh, ah, etc.)"
          />
          <FormHelperText>
            Automatically removes common filler sounds and words from transcriptions before sending to AI.
          </FormHelperText>
        </FormControl>

        {/* Deepgram Configuration */}
        {settings.speechService === 'deepgram' && (
          <>
            {apiKeyStatus && (
              <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, mt: 2 }}>
                <Typography variant="subtitle2">Deepgram API Status</Typography>
                <Typography
                  variant="body2"
                  color={apiKeyStatus.deepgram.configured ? 'success.main' : 'error.main'}
                >
                  {apiKeyStatus.deepgram.status}
                </Typography>
                {!apiKeyStatus.deepgram.configured && (
                  <Typography variant="caption" color="text.secondary">
                    Add NEXT_PUBLIC_DEEPGRAM_API_KEY to .env.local
                  </Typography>
                )}
              </Box>
            )}
            <TextField
              fullWidth margin="dense" name="deepgramLanguage" label="Recognition Language"
              value={settings.deepgramLanguage || 'en-US'} onChange={handleChange}
              helperText="E.g., en-US, es-ES, fr-FR" sx={{ mt: 2 }}
            />
          </>
        )}

        {/* Azure Configuration */}
        {settings.speechService === 'azure' && (
          <>
            {apiKeyStatus && (
              <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, mt: 2 }}>
                <Typography variant="subtitle2">Azure Speech API Status</Typography>
                <Typography
                  variant="body2"
                  color={apiKeyStatus.azure.configured ? 'success.main' : 'error.main'}
                >
                  {apiKeyStatus.azure.status}
                </Typography>
                {!apiKeyStatus.azure.configured && (
                  <Typography variant="caption" color="text.secondary">
                    Add NEXT_PUBLIC_AZURE_SPEECH_KEY and NEXT_PUBLIC_AZURE_SPEECH_REGION to .env.local
                  </Typography>
                )}
              </Box>
            )}
            <TextField
              fullWidth margin="dense" name="azureLanguage" label="Azure Recognition Language"
              value={settings.azureLanguage || 'en-US'} onChange={handleChange}
              helperText="E.g., en-US, es-ES" sx={{ mt: 2 }}
            />
          </>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button onClick={onClose} color="inherit">Cancel</Button>
        <Button onClick={handleSave} color="primary" variant="contained" startIcon={<SaveIcon />}>
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
}

SettingsDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func
};
