import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CodeIcon from '@mui/icons-material/Code';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import RecordVoiceOverIcon from '@mui/icons-material/RecordVoiceOver';
import SettingsIcon from '@mui/icons-material/Settings';
import SpeedIcon from '@mui/icons-material/Speed';
import { Avatar, Box, Button, Container, Grid, Paper, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import Head from 'next/head';
import Link from 'next/link'; // Import Next.js Link

const HeroSection = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`, // Gradient background
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(16, 2),
  textAlign: 'center',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '80vh', // Make hero section taller
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(1px)',
  }
}));

const FeaturePaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  textAlign: 'center',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'flex-start',
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.grey[200]}`,
  boxShadow: '0px 2px 8px rgba(0,0,0,0.04)',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0px 8px 25px rgba(79, 70, 229, 0.15)`,
    borderColor: theme.palette.primary.light,
  }
}));

const FeatureIcon = styled(Avatar)(({ theme }) => ({
  backgroundColor: `${theme.palette.primary.main}15`, // Very light primary color background
  color: theme.palette.primary.main,
  width: theme.spacing(8),
  height: theme.spacing(8),
  marginBottom: theme.spacing(3),
  border: `2px solid ${theme.palette.primary.main}25`,
}));

const Section = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 2),
}));

const Footer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.secondary,
  padding: theme.spacing(4, 2),
  textAlign: 'center',
  borderTop: `1px solid ${theme.palette.divider}`,
}));

const features = [
  {
    icon: <RecordVoiceOverIcon fontSize="large" />,
    title: 'Real-time Transcription',
    description: 'Accurate voice-to-text for all participants, powered by advanced speech recognition services.',
  },
  {
    icon: <QuestionAnswerIcon fontSize="large" />,
    title: 'AI-Powered Insights',
    description: 'Intelligent responses and suggestions with conversational context awareness using OpenAI/Gemini models.',
  },
  {
    icon: <CodeIcon fontSize="large" />,
    title: 'Code Formatting',
    description: 'Clear syntax highlighting for technical content, making code easy to read and understand.',
  },
  {
    icon: <SpeedIcon fontSize="large" />,
    title: 'Silence Detection',
    description: 'Automatically submit questions or responses after a configurable period of silence for a smoother flow.',
  },
  {
    icon: <SettingsIcon fontSize="large" />,
    title: 'Customizable Settings',
    description: 'Tailor AI models, API keys, and behavior to your specific needs and preferences.',
  },
];

export default function LandingPage() {
  return (
    <>
      <Head>
        <title>MyCopilot - Your AI-Powered Assistant</title>
        <meta name="description" content="Elevate your conversations with real-time transcription, AI insights, and seamless assistance. Perfect for meetings, calls, and discussions." />
        <link rel="icon" href="/favicon.ico" /> {/* Remember to add a favicon */}
      </Head>

      <HeroSection>
        <Container maxWidth="md">
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 700,
              letterSpacing: '-1px',
              color: 'white',
              position: 'relative',
              zIndex: 1
            }}
          >
            MyCopilot
          </Typography>
          <Typography
            variant="h5"
            component="p"
            paragraph
            sx={{
              mb: 4,
              color: 'rgba(255, 255, 255, 0.95)',
              position: 'relative',
              zIndex: 1,
              lineHeight: 1.6,
              maxWidth: '800px',
              margin: '0 auto 32px auto'
            }}
          >
            Elevate your conversations with AI-powered real-time transcription, intelligent suggestions, and seamless assistance. Focus on what matters, let us handle the notes.
          </Typography>
          <Link href="/interview" passHref>
            <Button
              variant="contained"
              size="large"
              endIcon={<ArrowForwardIcon />}
              sx={{
                background: 'rgba(255, 255, 255, 0.95)',
                color: (theme) => theme.palette.primary.main,
                padding: '16px 40px',
                fontSize: '1.2rem',
                fontWeight: 600,
                borderRadius: '50px',
                boxShadow: '0px 8px 25px rgba(255, 255, 255, 0.3)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                position: 'relative',
                zIndex: 1,
                '&:hover': {
                    background: 'rgba(255, 255, 255, 1)',
                    boxShadow: '0px 12px 35px rgba(255, 255, 255, 0.4)',
                    transform: 'translateY(-3px)'
                }
              }}
            >
              Start Assisting
            </Button>
          </Link>
        </Container>
      </HeroSection>

      <Section id="features" sx={{ backgroundColor: '#fafafa' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            align="center"
            gutterBottom
            sx={{
              mb: 8,
              fontWeight: 700,
              color: (theme) => theme.palette.primary.main,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-16px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '4px',
                background: (theme) => `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                borderRadius: '2px'
              }
            }}
          >
            Why Choose MyCopilot?
          </Typography>
          <Grid container spacing={4}>
            {features.map((feature) => (
              <Grid item xs={12} sm={6} md={4} key={feature.title}>
                <FeaturePaper elevation={3}> {/* Use elevation for subtle shadow */}
                  <FeatureIcon>{feature.icon}</FeatureIcon>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body1" color="textSecondary">
                    {feature.description}
                  </Typography>
                </FeaturePaper>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Section>

      <Section id="about" sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="md">
          <Typography
            variant="h3"
            component="h2"
            align="center"
            gutterBottom
            sx={{
              mb: 6,
              fontWeight: 700,
              color: (theme) => theme.palette.primary.main
            }}
          >
            About the Tool
          </Typography>
          <Typography
            variant="h5"
            component="p"
            align="center"
            color="textSecondary"
            paragraph
            sx={{
              mb: 4,
              lineHeight: 1.8,
              fontWeight: 400
            }}
          >
            MyCopilot is designed to be an indispensable assistant for your conversations and meetings. Whether you're conducting calls and need to capture key details, or you want to review important discussions, our tool provides the support you need.
          </Typography>
          <Typography
            variant="h5"
            component="p"
            align="center"
            color="textSecondary"
            paragraph
            sx={{
              lineHeight: 1.8,
              fontWeight: 400
            }}
          >
            Our mission is to make conversations more productive and insightful by leveraging the power of AI, allowing participants to focus on what truly matters: the content and ideas being discussed.
          </Typography>
        </Container>
      </Section>

      <Footer>
        <Typography variant="body2">
          &copy; {new Date().getFullYear()} MyCopilot. All rights reserved.
        </Typography>
        <Typography variant="caption" display="block" sx={{ mt: 1}}>
          Powered by AI for smarter conversations.
        </Typography>
      </Footer>
    </>
  );
}
