export const builtInModelGroups = [
  {
    name: "OpenAI Models",
    models: [
      // GPT-5 Family (Latest & Best - Released Aug 2025)
      { value: "gpt-5", label: "GPT-5 (Latest & Best for Coding/Agentic)" },
      { value: "gpt-5-mini", label: "GPT-5 Mini (Fast & Efficient)" },
      { value: "gpt-5-nano", label: "GPT-5 Nano (Fastest & Cheapest)" },
      { value: "gpt-5-chat-latest", label: "GPT-5 Chat (Non-reasoning)" },

      // GPT-4o Family (Current Generation)
      { value: "gpt-4o", label: "GPT-4o (Multimodal)" },
      { value: "gpt-4o-mini", label: "GPT-4o Mini (Fast)" },

      // Reasoning Models (o1 Series)
      { value: "o1", label: "o1 (Advanced Reasoning)" },
      { value: "o1-mini", label: "o1 Mini (Fast Reasoning)" },

      // GPT-4 Family (Still Supported)
      { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
      { value: "gpt-4", label: "GPT-4" },

      // GPT-3.5 Family (Legacy)
      { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo (Legacy)" },
    ]
  },
  {
    name: "Gemini Models",
    models: [
      // Gemini 2.5 Family (Latest & Best)
      { value: "gemini-2.5-pro", label: "Gemini 2.5 Pro (Latest & Best)" },
      { value: "gemini-2.5-flash", label: "Gemini 2.5 Flash (Recommended)" },
      { value: "gemini-2.5-flash-lite", label: "Gemini 2.5 Flash Lite (Fast)" },

      // Gemini 2.0 Family (Current)
      { value: "gemini-2.0-flash", label: "Gemini 2.0 Flash" },
      { value: "gemini-2.0-flash-lite", label: "Gemini 2.0 Flash Lite" },

      // Gemini 1.5 Family (Deprecated but still supported)
      { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash (Deprecated)" },
      { value: "gemini-1.5-flash-8b", label: "Gemini 1.5 Flash 8B (Deprecated)" },
      { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro (Deprecated)" },
    ]
  }
];


// Speech service options
export const speechServices = [
  { value: 'deepgram', label: 'Deepgram (Recommended - Fastest & Most Accurate)' },
  { value: 'azure', label: 'Azure Speech Services (Enterprise)' }
];

const defaultConfig = {
  openaiKey: '',
  geminiKey: '',
  aiModel: 'gpt-5-mini', // Latest GPT-5 model with excellent performance and efficiency
  silenceTimerDuration: 1.2,
  responseLength: 'medium',
  gptSystemPrompt: `You are an AI assistant that provides concise, helpful responses. Focus on being:
• Clear and direct in your answers
• Concise - avoid lengthy explanations unless specifically requested
• Professional and informative
• Structured in your responses when appropriate
• Helpful in providing actionable insights

When responding to questions or discussions:
• If the context is unclear, ask for clarification
• Provide focused, relevant information
• Highlight key points and important details
• Keep responses brief but comprehensive
• Maintain a professional, supportive tone`,

  // Speech Service Configuration
  speechService: 'deepgram', // Default to Deepgram (better performance and cost)

  // Deepgram Configuration
  deepgramKey: '',
  deepgramModel: 'nova-2', // Latest Deepgram model
  deepgramLanguage: 'en-US',

  // Azure Speech Configuration (Legacy support)
  azureToken: '',
  azureRegion: 'eastus',
  azureLanguage: 'en-US',

  customModels: [], // Array for user-added models { value: 'model-id', label: 'Display Name', type: 'openai' | 'gemini' }
  systemAutoMode: true,
  isManualMode: false,
  // Advanced settings for reasoning models
  reasoningEffort: 'medium', // low, medium, high (for o1 and future reasoning models)
  verbosity: 1, // 0-2 for response detail level (for advanced models)
  thinkingBudget: null, // null = default, 0 = disabled, number = custom budget for Gemini 2.5 thinking
};

export function getConfig() {
  // Get API keys from environment variables (server-side secure)
  const envConfig = {
    deepgramKey: process.env.NEXT_PUBLIC_DEEPGRAM_API_KEY || '',
    azureToken: process.env.NEXT_PUBLIC_AZURE_SPEECH_KEY || '',
    azureRegion: process.env.NEXT_PUBLIC_AZURE_SPEECH_REGION || '',
    openaiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
    geminiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || ''
  };

  if (typeof window !== 'undefined') {
    const storedConfig = localStorage.getItem('myCopilotConfig');
    let parsed = storedConfig ? JSON.parse(storedConfig) : {};

    // Migrate old config format for aiModel if gptModel exists
    if (parsed.gptModel && !parsed.aiModel) {
      parsed.aiModel = parsed.gptModel;
      delete parsed.gptModel;
    }
    // Ensure customModels is an array
    if (!Array.isArray(parsed.customModels)) {
        parsed.customModels = [];
    }

    // Remove API keys from localStorage if they exist (security migration)
    if (parsed.deepgramKey || parsed.azureToken || parsed.openaiKey || parsed.geminiKey) {
      console.log('🔒 Migrating API keys from localStorage to environment variables for security');
      delete parsed.deepgramKey;
      delete parsed.azureToken;
      delete parsed.openaiKey;
      delete parsed.geminiKey;
      // Save cleaned config back to localStorage
      localStorage.setItem('myCopilotConfig', JSON.stringify(parsed));
    }

    // Merge configs: defaultConfig < localStorage < environment variables
    return { ...defaultConfig, ...parsed, ...envConfig };
  }
  return { ...defaultConfig, ...envConfig };
}

export function setConfig(config) {
  if (typeof window !== 'undefined') {
    // Remove API keys from config before saving to localStorage (security)
    const { deepgramKey, azureToken, azureRegion, openaiKey, geminiKey, ...configToSave } = config;

    // Ensure customModels is an array before saving
    configToSave.customModels = Array.isArray(config.customModels) ? config.customModels : [];

    localStorage.setItem('myCopilotConfig', JSON.stringify(configToSave));
    console.log('💾 Configuration saved (API keys stored securely in environment variables)');
  }
}

// Utility function to check API key status
export function getApiKeyStatus() {
  const config = getConfig();
  return {
    deepgram: {
      configured: !!config.deepgramKey,
      status: config.deepgramKey ? 'Active' : 'Not Configured'
    },
    azure: {
      configured: !!(config.azureToken && config.azureRegion),
      status: (config.azureToken && config.azureRegion) ? 'Active' : 'Not Configured'
    },
    openai: {
      configured: !!config.openaiKey,
      status: config.openaiKey ? 'Active' : 'Not Configured'
    },
    gemini: {
      configured: !!config.geminiKey,
      status: config.geminiKey ? 'Active' : 'Not Configured'
    }
  };
}
